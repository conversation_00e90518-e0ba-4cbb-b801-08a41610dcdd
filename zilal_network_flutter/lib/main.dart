import 'package:flutter/material.dart';

void main() {
  runApp(const ZilalNetworkGuardianApp());
}

class ZilalNetworkGuardianApp extends StatelessWidget {
  const ZilalNetworkGuardianApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Zilal Network Guardian',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1A237E),
          brightness: Brightness.light,
        ),
      ),
      darkTheme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF1A237E),
          brightness: Brightness.dark,
        ),
      ),
      themeMode: ThemeMode.system,
      home: const DashboardPage(),
    );
  }
}

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const DashboardScreen(),
    const ScannerScreen(),
    const DevicesScreen(),
    const SecurityScreen(),
    const SettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.radar),
            label: 'Scanner',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.devices),
            label: 'Devices',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.security),
            label: 'Security',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }
}

// Dashboard Screen
class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Network Status Card
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.wifi,
                          color: Colors.green,
                          size: 24,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'Network Status',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Status'),
                            Text(
                              'Connected',
                              style: TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Network'),
                            Text(
                              '192.168.1.0/24',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Devices'),
                            Text(
                              '12 Found',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Security Score Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Security Score',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Center(
                      child: SizedBox(
                        height: 150,
                        width: 150,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              height: 150,
                              width: 150,
                              child: CircularProgressIndicator(
                                value: 0.85,
                                strokeWidth: 12,
                                backgroundColor: Colors.grey[300],
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  Colors.green,
                                ),
                              ),
                            ),
                            const Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '85',
                                  style: TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'Secure',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.green,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Scanner Screen
class ScannerScreen extends StatefulWidget {
  const ScannerScreen({super.key});

  @override
  State<ScannerScreen> createState() => _ScannerScreenState();
}

class _ScannerScreenState extends State<ScannerScreen>
    with TickerProviderStateMixin {
  bool _isScanning = false;
  double _scanProgress = 0.0;
  String _currentScanTarget = '';
  String _selectedScanType = 'Quick Scan';
  late AnimationController _radarController;
  final TextEditingController _targetController = TextEditingController();

  final List<String> _scanTypes = [
    'Quick Scan',
    'Deep Scan',
    'Port Scan',
    'Vulnerability Scan',
  ];

  final List<Map<String, dynamic>> _scanResults = [];
  final List<String> _scanLog = [];

  @override
  void initState() {
    super.initState();
    _radarController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _targetController.text = '192.168.1.0/24';
  }

  @override
  void dispose() {
    _radarController.dispose();
    _targetController.dispose();
    super.dispose();
  }

  Future<void> _startNetworkScan() async {
    setState(() {
      _isScanning = true;
      _scanProgress = 0.0;
      _scanResults.clear();
      _scanLog.clear();
      _currentScanTarget = _targetController.text;
    });

    _radarController.repeat();
    _addToLog('Starting ${_selectedScanType.toLowerCase()} on ${_targetController.text}');

    // Simulate network scanning with realistic progress
    await _performNetworkScan();

    _radarController.stop();
    setState(() {
      _isScanning = false;
      _scanProgress = 1.0;
    });
    _addToLog('Scan completed. Found ${_scanResults.length} devices.');
  }

  Future<void> _performNetworkScan() async {
    // Simulate scanning different IP addresses
    final baseIp = _targetController.text.split('/')[0];
    final ipParts = baseIp.split('.');
    final networkBase = '${ipParts[0]}.${ipParts[1]}.${ipParts[2]}';

    // Simulate scanning 20 IP addresses
    for (int i = 1; i <= 20; i++) {
      if (!_isScanning) break;

      final currentIp = '$networkBase.$i';
      _addToLog('Scanning $currentIp...');

      setState(() {
        _scanProgress = i / 20;
        _currentScanTarget = currentIp;
      });

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 300));

      // Simulate finding devices (30% chance)
      if (i <= 6) {
        final device = _generateMockDevice(currentIp, i);
        setState(() {
          _scanResults.add(device);
        });
        _addToLog('Device found: ${device['hostname']} (${device['ip']})');
      }
    }
  }

  Map<String, dynamic> _generateMockDevice(String ip, int index) {
    final devices = [
      {'hostname': 'Router', 'type': 'Router', 'ports': [80, 443], 'security': 'Secure'},
      {'hostname': 'Desktop-PC', 'type': 'Computer', 'ports': [22, 80, 3389], 'security': 'Warning'},
      {'hostname': 'Smartphone', 'type': 'Mobile', 'ports': [], 'security': 'Secure'},
      {'hostname': 'IoT-Camera', 'type': 'IoT', 'ports': [80, 554], 'security': 'Vulnerable'},
      {'hostname': 'Smart-TV', 'type': 'Media', 'ports': [8080], 'security': 'Secure'},
      {'hostname': 'Printer', 'type': 'Printer', 'ports': [631, 9100], 'security': 'Warning'},
    ];

    final deviceIndex = (index - 1) % devices.length;
    final device = devices[deviceIndex];

    return {
      'ip': ip,
      'hostname': device['hostname'],
      'type': device['type'],
      'ports': device['ports'],
      'security': device['security'],
      'lastSeen': 'Just now',
      'responseTime': '${10 + (index * 5)}ms',
    };
  }

  void _addToLog(String message) {
    setState(() {
      _scanLog.add('${DateTime.now().toString().substring(11, 19)} - $message');
    });
  }

  void _stopScan() {
    setState(() {
      _isScanning = false;
    });
    _radarController.stop();
    _addToLog('Scan stopped by user.');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Scanner'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _showScanHistory(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildScanConfiguration(),
            const SizedBox(height: 16),
            _buildScanControls(),
            const SizedBox(height: 16),
            if (_isScanning) _buildScanProgress(),
            if (_isScanning) const SizedBox(height: 16),
            _buildScanResults(),
            const SizedBox(height: 16),
            _buildScanLog(),
          ],
        ),
      ),
    );
  }

  Widget _buildScanConfiguration() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scan Configuration',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedScanType,
              decoration: const InputDecoration(
                labelText: 'Scan Type',
                border: OutlineInputBorder(),
              ),
              items: _scanTypes.map((type) {
                return DropdownMenuItem(value: type, child: Text(type));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedScanType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _targetController,
              decoration: const InputDecoration(
                labelText: 'Target Network',
                hintText: '192.168.1.0/24',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.network_check),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isScanning ? _stopScan : _startNetworkScan,
                    icon: Icon(_isScanning ? Icons.stop : Icons.play_arrow),
                    label: Text(_isScanning ? 'Stop Scan' : 'Start Scan'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isScanning ? Colors.red : Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
              ],
            ),
            if (_isScanning) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  RotationTransition(
                    turns: _radarController,
                    child: const Icon(Icons.radar, color: Colors.blue, size: 24),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Scanning $_currentScanTarget...',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildScanProgress() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Scan Progress',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${(_scanProgress * 100).toInt()}%',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: _scanProgress,
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
            const SizedBox(height: 8),
            Text(
              'Found ${_scanResults.length} devices so far...',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanResults() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Scan Results',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  '${_scanResults.length} devices found',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_scanResults.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(Icons.search, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text(
                        'No devices found yet',
                        style: TextStyle(color: Colors.grey),
                      ),
                      Text(
                        'Start a scan to discover network devices',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _scanResults.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final device = _scanResults[index];
                  return _buildDeviceResultItem(device);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceResultItem(Map<String, dynamic> device) {
    Color securityColor;
    IconData securityIcon;

    switch (device['security']) {
      case 'Secure':
        securityColor = Colors.green;
        securityIcon = Icons.check_circle;
        break;
      case 'Warning':
        securityColor = Colors.orange;
        securityIcon = Icons.warning;
        break;
      case 'Vulnerable':
        securityColor = Colors.red;
        securityIcon = Icons.error;
        break;
      default:
        securityColor = Colors.grey;
        securityIcon = Icons.help;
    }

    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: securityColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          _getDeviceIcon(device['type']),
          color: securityColor,
        ),
      ),
      title: Text(
        device['hostname'],
        style: const TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${device['ip']} • ${device['responseTime']}'),
          if (device['ports'].isNotEmpty)
            Text(
              'Open ports: ${device['ports'].join(', ')}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(securityIcon, color: securityColor, size: 20),
          const SizedBox(width: 4),
          Text(
            device['security'],
            style: TextStyle(
              color: securityColor,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
      onTap: () => _showDeviceDetails(device),
    );
  }

  IconData _getDeviceIcon(String type) {
    switch (type) {
      case 'Router':
        return Icons.router;
      case 'Computer':
        return Icons.computer;
      case 'Mobile':
        return Icons.smartphone;
      case 'IoT':
        return Icons.camera_alt;
      case 'Media':
        return Icons.tv;
      case 'Printer':
        return Icons.print;
      default:
        return Icons.device_unknown;
    }
  }

  Widget _buildScanLog() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Scan Log',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: _scanLog.isEmpty
                  ? const Center(
                      child: Text(
                        'Scan log will appear here...',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(8),
                      itemCount: _scanLog.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _scanLog[index],
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeviceDetails(Map<String, dynamic> device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(device['hostname']),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('IP Address', device['ip']),
            _buildDetailRow('Device Type', device['type']),
            _buildDetailRow('Security Status', device['security']),
            _buildDetailRow('Response Time', device['responseTime']),
            if (device['ports'].isNotEmpty)
              _buildDetailRow('Open Ports', device['ports'].join(', ')),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement detailed scan
            },
            child: const Text('Detailed Scan'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showScanHistory(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Scan History'),
        content: const Text('Scan history feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Devices Screen
class DevicesScreen extends StatelessWidget {
  const DevicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Devices'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.devices, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'Network Devices',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Manage and monitor your network devices'),
          ],
        ),
      ),
    );
  }
}

// Security Screen
class SecurityScreen extends StatelessWidget {
  const SecurityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Security Reports'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.security, size: 64, color: Colors.orange),
            SizedBox(height: 16),
            Text(
              'Security Reports',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('View security analysis and threat reports'),
          ],
        ),
      ),
    );
  }
}

// Settings Screen
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.settings, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Settings',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Configure app settings and preferences'),
          ],
        ),
      ),
    );
  }
}
